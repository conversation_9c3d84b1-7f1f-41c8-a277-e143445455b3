from importlib import import_module
from types import ModuleType
from .qmt_data import QmtData
from .qmt_trader import QmtTrader
from .taos_database import TaosDatabase
from .cash_data import CashManager
import pandas as pd
class ContextManager:   
    """上下文类"""
    def __init__(self) -> None:
        self._cache = {}
        self.init_default()

    def get_obj(self, key: str):
        return self._cache.get(key, None)
    
    def module_obj(self,  key: str, module_name: str=None, class_name: str=None, *args, **kwargs):
        """获取对象"""
        obj = self._cache.get(key, None)
        if obj:
            return obj
        if not module_name:
            print(f"关键字 {key}, 请先初始化.")
            return
        try:
            module: ModuleType = import_module(f"{module_name}")
            obj = getattr(module, class_name)(*args, **kwargs)
        except ModuleNotFoundError:
            print(f"找不到类库驱动{module_name}")
            return
        self._cache[key] = obj
        return obj

    def init_default(self):
        """初始化默认对象"""
        self._cache["qmt_data"] = QmtData()
        self._cache["qmt_trader"] = QmtTrader()
        self._cache["cash_manager"] = CashManager()
        self._cache["taos_client"] = TaosDatabase()
    
    # 获取指定

context_manager = ContextManager()






