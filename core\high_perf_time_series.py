"""
高性能时间序列数据管理系统（单股票DataFrame管理）
- 假设按时间顺序插入（时间戳严格递增，且不重复，去重在上层已处理）
- 所有值均为int或float，列名为字符串
- 关注极致的内存效率与读写性能
- 单实例只管理一个DataFrame（单只股票），多股票由上层应用协调（推荐）
- 注释全部中文
"""

from __future__ import annotations
import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)


class TimeSeriesManager:
    """
    高性能时间序列数据管理器（单股票版）

    设计要点：
    - 严格按时间递增插入：无需排序、无需重复检查
    - 预分配容量 + 倍增扩容
    - 预计算列索引，读写使用iat（位置访问）以获得最佳性能
    - 可选时间戳映射（timestamp -> row），换取少量内存以获得O(1)定位
    - 读取不copy，直接返回切片视图（节省内存；注意外部不要原地修改）
    """

    def __init__(
        self,
        columns: List[str],
        initial_capacity: int = 10000,
        enable_ts_map: bool = False,
    ) -> None:
        """
        Args:
            columns: 列名，必须包含 'timestamp'
            initial_capacity: 初始容量（行数预分配）
            enable_ts_map: 是否使用时间戳->行索引的映射（提升按时间戳查找性能，略增内存）
        """
        if 'timestamp' not in columns:
            raise ValueError("columns必须包含'timestamp'列")

        self.columns: List[str] = list(columns)
        self.capacity: int = max(1, int(initial_capacity))
        self.used: int = 0  # 已使用的行数
        self.enable_ts_map: bool = bool(enable_ts_map)

        # 为不同列设置合理dtype：timestamp使用int64，其余使用float64
        data_dict = {c: np.empty(self.capacity, dtype=np.float64) for c in self.columns}
        data_dict['timestamp'] = np.empty(self.capacity, dtype=np.int64)
        self.df: pd.DataFrame = pd.DataFrame(data_dict, columns=self.columns)

        # 列名 -> 列位置 索引缓存
        self.col_idx: Dict[str, int] = {c: i for i, c in enumerate(self.columns)}
        self.ts_col: int = self.col_idx['timestamp']

        # 可选：时间戳 -> 行索引 映射
        self.ts_map: Optional[Dict[int, int]] = {} if self.enable_ts_map else None

    # -------- 写入 --------
    def _ensure_capacity(self, need: int = 1) -> None:
        """确保容量足够，必要时按倍数扩容（最小翻倍）。"""
        if self.used + need <= self.capacity:
            return
        # 扩容：至少翻倍直到满足
        new_cap = self.capacity
        while new_cap < self.used + need:
            new_cap *= 2
        # 为每列扩充底层ndarray
        for c in self.columns:
            arr = self.df[c].to_numpy(copy=False)
            new_arr = np.empty(new_cap, dtype=arr.dtype)
            new_arr[: self.used] = arr[: self.used]
            self.df[c] = new_arr  # 直接替换底层数组引用
        self.capacity = new_cap
        logger.info(f"DataFrame扩容: {self.capacity}")

    def insert(self, timestamp: int, values: Dict[str, Union[int, float]]) -> int:
        """
        写入一行（按时间严格递增，无需排序/去重）。
        返回写入行的行号。
        """
        # 容量检查
        self._ensure_capacity(1)

        row = self.used
        # 写入timestamp
        self.df.iat[row, self.ts_col] = int(timestamp)

        # 写入其他列值（缺失列默认置0）
        for col, v in values.items():
            if col == 'timestamp':
                continue
            idx = self.col_idx.get(col)
            if idx is not None:
                self.df.iat[row, idx] = float(v)
        # 填充未提供的列为0
        for col, idx in self.col_idx.items():
            if col != 'timestamp' and col not in values:
                self.df.iat[row, idx] = 0.0

        if self.ts_map is not None:
            self.ts_map[int(timestamp)] = row

        self.used += 1
        return row

    def batch_insert(self, rows: List[Dict[str, Union[int, float]]]) -> int:
        """
        批量写入（数据已按时间递增且无重复）。
        返回成功写入的行数。
        """
        n = len(rows)
        if n == 0:
            return 0
        self._ensure_capacity(n)
        start_row = self.used
        for i, r in enumerate(rows):
            ts = int(r['timestamp'])  # 必须存在
            row = start_row + i
            self.df.iat[row, self.ts_col] = ts
            for col, v in r.items():
                if col == 'timestamp':
                    continue
                idx = self.col_idx.get(col)
                if idx is not None:
                    self.df.iat[row, idx] = float(v)
            # 填充缺失列为0
            for col, idx in self.col_idx.items():
                if col != 'timestamp' and col not in r:
                    self.df.iat[row, idx] = 0.0
            if self.ts_map is not None:
                self.ts_map[ts] = row
        self.used += n
        return n

    # -------- 读取：索引/范围 --------
    def get_rows(self, start_idx: int, count: int) -> Optional[pd.DataFrame]:
        """按起始索引和数量获取行的视图（不copy）。"""
        if start_idx < 0 or count <= 0:
            return None
        end_idx = min(self.used, start_idx + count)
        if start_idx >= end_idx:
            return None
        return self.df.iloc[start_idx:end_idx]

    def get_row_range(self, start_idx: int, end_idx: int) -> Optional[pd.DataFrame]:
        """按行范围[start_idx, end_idx)获取视图（不copy）。"""
        if start_idx < 0 or end_idx <= start_idx:
            return None
        end_idx = min(self.used, end_idx)
        if start_idx >= end_idx:
            return None
        return self.df.iloc[start_idx:end_idx]

    # -------- 读取：时间戳定位 --------
    def _search_ts_pos(self, ts: int, side: str = 'left') -> int:
        """
        在已使用范围内，对timestamp列执行二分定位。
        side='left' 返回第一个>=ts的位置；'right' 返回第一个>ts的位置。
        返回位置可能等于self.used（越界）。
        """
        arr = self.df['timestamp'].to_numpy(copy=False)[: self.used]
        if side == 'right':
            return int(np.searchsorted(arr, ts, side='right'))
        return int(np.searchsorted(arr, ts, side='left'))

    def get_row_by_timestamp(self, timestamp: int, fallback: str = 'next') -> Optional[pd.Series]:
        """按时间戳获取行（不copy）。fallback: 'next'|'prev'|'none'。"""
        if self.used == 0:
            return None
        # 优先使用哈希映射
        if self.ts_map is not None:
            row = self.ts_map.get(int(timestamp))
            if row is not None:
                return self.df.iloc[row]
        # 二分定位
        pos_left = self._search_ts_pos(timestamp, 'left')
        if pos_left < self.used and int(self.df.iat[pos_left, self.ts_col]) == int(timestamp):
            return self.df.iloc[pos_left]
        if fallback == 'none':
            return None
        if fallback == 'next':
            pos = self._search_ts_pos(timestamp, 'right')
            if pos < self.used:
                return self.df.iloc[pos]
            return None
        if fallback == 'prev':
            pos = self._search_ts_pos(timestamp, 'left')
            if pos > 0:
                return self.df.iloc[pos - 1]
            return None
        return None

    def get_range_by_timestamp(self, start_ts: int, end_ts: int, fallback: str = 'next') -> Optional[pd.DataFrame]:
        """
        按时间范围获取行视图（不copy）。
        默认对不存在的边界使用'next'策略。
        """
        if self.used == 0:
            return None
        # 起点
        start_pos = self._search_ts_pos(start_ts, 'left')
        if start_pos < self.used and int(self.df.iat[start_pos, self.ts_col]) == int(start_ts):
            pass
        else:
            if fallback == 'prev':
                start_pos = max(0, start_pos - 1)
            elif fallback == 'none' and (start_pos >= self.used or int(self.df.iat[start_pos, self.ts_col]) != int(start_ts)):
                return None
        # 终点（包含end_ts）
        end_pos = self._search_ts_pos(end_ts, 'right')
        if start_pos >= end_pos:
            return None
        end_pos = min(end_pos, self.used)
        return self.df.iloc[start_pos:end_pos]

    # -------- 读取：单元格 --------
    def get_cell(self, row_index: int, column_name: str) -> Optional[Union[int, float]]:
        """获取单元格数值（使用iat，O(1)）。"""
        if row_index < 0 or row_index >= self.used:
            return None
        idx = self.col_idx.get(column_name)
        if idx is None:
            return None
        return self.df.iat[row_index, idx]

    def get_value_by_timestamp(self, timestamp: int, column_name: str, fallback: str = 'next') -> Optional[Union[int, float]]:
        """按时间戳+列名获取数值（O(logN) 或 O(1)）。"""
        row = self.get_row_by_timestamp(timestamp, fallback)
        if row is None:
            return None
        idx = self.col_idx.get(column_name)
        if idx is None:
            return None
        # row是Series视图，使用iat更快：需要行号，故重新定位
        if self.ts_map is not None:
            r = self.ts_map.get(int(row['timestamp']))
            if r is not None:
                return self.df.iat[r, idx]
        # 否则基于row.name（行号）
        return self.df.iat[int(row.name), idx]

    # -------- 其它工具 --------
    def get_latest(self, count: int = 1) -> Optional[pd.DataFrame]:
        """获取最新N行（视图）。"""
        if self.used == 0:
            return None
        start = max(0, self.used - count)
        return self.df.iloc[start:self.used]

    def size(self) -> int:
        """返回当前已使用的行数。"""
        return self.used

    def get_memory_usage_mb(self) -> float:
        """估算当前DataFrame内存（MB）。"""
        return float(self.df.memory_usage(deep=False).sum()) / (1024 * 1024)


class MultiTimeSeriesManager:
    """
    统一管理版：一个DataFrame存放多只股票（不存股票字符串列），
    按股票分段（连续内存）存储，最大化内存与速度。
    - 每只股票分配相同的初始容量，按需整体倍增扩容
    - 行内仅存在数值（timestamp:int64，其余float64），股票码只在映射表保存一次
    - 所有读取均返回视图（不copy）
    """
    def __init__(self, columns: List[str], stock_codes: List[str], per_stock_initial_capacity: int = 10000, enable_ts_map: bool = False) -> None:
        if 'timestamp' not in columns:
            raise ValueError("columns必须包含'timestamp'列")
        if not stock_codes:
            raise ValueError("stock_codes不能为空")
        self.columns = list(columns)
        self.n = len(stock_codes)
        self.per_cap = max(1, int(per_stock_initial_capacity))
        self.enable_ts_map = bool(enable_ts_map)
        # 代码映射
        self.codes = list(stock_codes)
        self.code_to_idx: Dict[str, int] = {c: i for i, c in enumerate(self.codes)}
        # 每股已用行数
        self.used = np.zeros(self.n, dtype=np.int64)
        # 列数组（总长度 = n * per_cap）
        total_len = self.n * self.per_cap
        data_dict = {c: np.empty(total_len, dtype=np.float64) for c in self.columns}
        data_dict['timestamp'] = np.empty(total_len, dtype=np.int64)
        self.df: pd.DataFrame = pd.DataFrame(data_dict, columns=self.columns)
        self.col_idx: Dict[str, int] = {c: i for i, c in enumerate(self.columns)}
        self.ts_col: int = self.col_idx['timestamp']
        # 可选时间戳映射：每股一个小字典，映射相对行号
        self.ts_map: Optional[Dict[str, Dict[int, int]]] = None
        if self.enable_ts_map:
            self.ts_map = {c: {} for c in self.codes}

    def _base(self, code: str) -> int:
        i = self.code_to_idx.get(code)
        if i is None:
            raise KeyError(f"未知股票代码: {code}")
        return i * self.per_cap

    def _ensure_capacity_for(self, i: int, need: int = 1) -> None:
        if self.used[i] + need <= self.per_cap:
            return
        # 整体倍增扩容（保持每股连续段）
        new_per = self.per_cap
        while new_per < self.used[i] + need:
            new_per *= 2
        new_total = new_per * self.n
        for c in self.columns:
            arr = self.df[c].to_numpy(copy=False)
            new_arr = np.empty(new_total, dtype=arr.dtype)
            # 按股票拷贝旧数据到新分段
            for idx in range(self.n):
                old_start = idx * self.per_cap
                old_end = old_start + self.used[idx]
                new_start = idx * new_per
                if old_end > old_start:
                    new_arr[new_start:new_start + (old_end - old_start)] = arr[old_start:old_end]
            self.df[c] = new_arr
        self.per_cap = new_per

    def insert(self, code: str, timestamp: int, values: Dict[str, Union[int, float]]) -> int:
        i = self.code_to_idx.get(code)
        if i is None:
            raise KeyError(f"未知股票代码: {code}")
        self._ensure_capacity_for(i, 1)
        base = i * self.per_cap
        row = base + int(self.used[i])
        # timestamp
        self.df.iat[row, self.ts_col] = int(timestamp)
        # 其他列
        for col, v in values.items():
            if col == 'timestamp':
                continue
            idx = self.col_idx.get(col)
            if idx is not None:
                self.df.iat[row, idx] = float(v)
        # 缺省列置0
        for col, idx in self.col_idx.items():
            if col != 'timestamp' and col not in values:
                self.df.iat[row, idx] = 0.0
        # ts_map
        if self.ts_map is not None:
            self.ts_map[code][int(timestamp)] = int(self.used[i])
        self.used[i] += 1
        return row

    def batch_insert(self, code: str, rows: List[Dict[str, Union[int, float]]]) -> int:
        i = self.code_to_idx.get(code)
        if i is None:
            raise KeyError(f"未知股票代码: {code}")
        n = len(rows)
        if n == 0:
            return 0
        self._ensure_capacity_for(i, n)
        base = i * self.per_cap
        start = base + int(self.used[i])
        for k, r in enumerate(rows):
            ts = int(r['timestamp'])
            row = start + k
            self.df.iat[row, self.ts_col] = ts
            for col, v in r.items():
                if col == 'timestamp':
                    continue
                idx = self.col_idx.get(col)
                if idx is not None:
                    self.df.iat[row, idx] = float(v)
            for col, idx in self.col_idx.items():
                if col != 'timestamp' and col not in r:
                    self.df.iat[row, idx] = 0.0
            if self.ts_map is not None:
                self.ts_map[code][ts] = int(self.used[i]) + k
        self.used[i] += n
        return n

    def _ts_array(self, i: int):
        base = i * self.per_cap
        u = int(self.used[i])
        return self.df['timestamp'].to_numpy(copy=False)[base:base+u], base, u

    # ---- 读取：索引/范围 ----
    def get_rows(self, code: str, start_idx: int, count: int) -> Optional[pd.DataFrame]:
        i = self.code_to_idx.get(code)
        if i is None or start_idx < 0 or count <= 0:
            return None
        u = int(self.used[i])
        end = min(u, start_idx + count)
        if start_idx >= end:
            return None
        base = i * self.per_cap
        return self.df.iloc[base+start_idx:base+end]

    def get_row_range(self, code: str, start_idx: int, end_idx: int) -> Optional[pd.DataFrame]:
        i = self.code_to_idx.get(code)
        if i is None or start_idx < 0 or end_idx <= start_idx:
            return None
        u = int(self.used[i])
        end = min(u, end_idx)
        if start_idx >= end:
            return None
        base = i * self.per_cap
        return self.df.iloc[base+start_idx:base+end]

    # ---- 读取：时间戳 ----
    def get_row_by_timestamp(self, code: str, timestamp: int, fallback: str = 'next') -> Optional[pd.Series]:
        i = self.code_to_idx.get(code)
        if i is None:
            return None
        if self.ts_map is not None:
            r = self.ts_map[code].get(int(timestamp))
            if r is not None:
                base = i * self.per_cap
                return self.df.iloc[base + r]
        arr, base, u = self._ts_array(i)
        if u == 0:
            return None
        posL = int(np.searchsorted(arr, timestamp, side='left'))
        if posL < u and int(arr[posL]) == int(timestamp):
            return self.df.iloc[base + posL]
        if fallback == 'none':
            return None
        if fallback == 'next':
            pos = int(np.searchsorted(arr, timestamp, side='right'))
            if pos < u:
                return self.df.iloc[base + pos]
            return None
        if fallback == 'prev':
            pos = int(np.searchsorted(arr, timestamp, side='left'))
            if pos > 0:
                return self.df.iloc[base + pos - 1]
            return None
        return None

    def get_range_by_timestamp(self, code: str, start_ts: int, end_ts: int, fallback: str = 'next') -> Optional[pd.DataFrame]:
        i = self.code_to_idx.get(code)
        if i is None:
            return None
        arr, base, u = self._ts_array(i)
        if u == 0:
            return None
        s = int(np.searchsorted(arr, start_ts, side='left'))
        if not (s < u and int(arr[s]) == int(start_ts)):
            if fallback == 'prev':
                s = max(0, s-1)
            elif fallback == 'none':
                return None
        e = int(np.searchsorted(arr, end_ts, side='right'))
        if s >= e:
            return None
        e = min(e, u)
        return self.df.iloc[base+s:base+e]

    # ---- 单元格/值 ----
    def get_cell(self, code: str, row_index: int, column_name: str) -> Optional[Union[int, float]]:
        i = self.code_to_idx.get(code)
        if i is None:
            return None
        u = int(self.used[i])
        if row_index < 0 or row_index >= u:
            return None
        idx = self.col_idx.get(column_name)
        if idx is None:
            return None
        base = i * self.per_cap
        return self.df.iat[base + row_index, idx]

    def get_value_by_timestamp(self, code: str, timestamp: int, column_name: str, fallback: str = 'next') -> Optional[Union[int, float]]:
        row = self.get_row_by_timestamp(code, timestamp, fallback)
        if row is None:
            return None
        idx = self.col_idx.get(column_name)
        if idx is None:
            return None
        return self.df.iat[int(row.name), idx]

    def get_latest(self, code: str, count: int = 1) -> Optional[pd.DataFrame]:
        i = self.code_to_idx.get(code)
        if i is None:
            return None
        u = int(self.used[i])
        if u == 0:
            return None
        start = max(0, u - count)
        base = i * self.per_cap
        return self.df.iloc[base+start:base+u]

    # ---- 统计 ----
    def size(self, code: Optional[str] = None) -> int:
        if code is None:
            return int(self.used.sum())
        i = self.code_to_idx.get(code)
        return int(self.used[i]) if i is not None else 0

    def get_memory_usage_mb(self) -> float:
        return float(self.df.memory_usage(deep=False).sum()) / (1024 * 1024)



# 使用示例（请在上层负责多股票创建多个实例）
if __name__ == '__main__':
    cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'amount']
    ts = TimeSeriesManager(cols, initial_capacity=8, enable_ts_map=False)

    import time
    base = int(time.time())
    # 插入演示
    for i in range(5):
        ts.insert(base + i * 3, {
            'open': 10 + i * 0.1,
            'high': 10.2 + i * 0.1,
            'low': 9.8 + i * 0.1,
            'close': 10.1 + i * 0.1,
            'volume': 1000 + i * 10,
            'amount': 10000 + i * 100,
        })

    print('size=', ts.size(), 'mem(MB)=', f'{ts.get_memory_usage_mb():.3f}')
    print('rows 1~3:\n', ts.get_rows(1, 3))
    print('by ts exact: ', ts.get_row_by_timestamp(base + 6))
    print('by ts next:  ', ts.get_row_by_timestamp(base + 7, 'next'))
    print('range:       \n', ts.get_range_by_timestamp(base + 3, base + 10))
    print('cell:        ', ts.get_cell(2, 'close'))
    print('val by ts:   ', ts.get_value_by_timestamp(base + 9, 'volume'))
