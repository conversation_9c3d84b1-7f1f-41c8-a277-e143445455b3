import time
import logging
import threading 
import datetime
from core.context import context_manager
from core.setting import SETTINGS


taos_client = context_manager.get_obj("taos_client")

taos_client.connect(SETTINGS)
topic_name = "tick"
# 指定 年月日
time.sleep(3)
start_time = datetime.datetime(year=2025, month=8, day=13, hour=9, minute=0, second=0)
end_time = datetime.datetime(year=2025, month=8, day=13, hour=9, minute=32, second=0)
taos_client.create_tick_topic(topic_name, start_time, end_time)
# 启动行情缓存
thread = threading.Thread(target=taos_client.subscribe, args=(topic_name,))
# thread = threading.Thread(target=taos_client.subscribe_featch, args=(start_time, end_time))
thread.start()

time.sleep(20)

