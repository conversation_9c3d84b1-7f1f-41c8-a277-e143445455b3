import bisect
from queue import Empty, Queue
from .object import TickData
import threading 
import os 
from .setting import SETTINGS, data_dir
from .utility import read_csv, get_stock_ratio
from .excel import ExcelReader

class CashTick:
    name = "cash_tick"
    def __init__(self) -> None:
        self.timestamps = []   # 存储时间戳（如 93000, 93003, 93006...），已排序
        self.values: list[TickData] = []       # 对应的数据（价格、成交量等）
        self.ts_index: dict[int, int] = {}  # 时间戳到数据的映射
        self.index = -1
        self.limit_up_price = 0
        self.limit_down_price = 0
        self.limit_ratio = 0
        self.total_volume = 0
        self.float_volume = 0
        # 所有5档委托买入量
        self.bid_vol = 0
        # 所有5档委托卖出量
        self.ask_vol = 0
        # 涨停的索引 (买一价等于涨停价一样)
        self.up_stop_index = []
        # 卖一价等于涨停价的索引
        self.ask1_up_stop_index = []
        # 跌停的索引
        self.down_stop_index = []
        self.stop_status = 0 # 0: 1:涨停 -1：跌停  2: 卖一价等于涨停价(涨停前)
        # 昨日总成交笔数
        self.pre_today_transaction_num = 0
        # 昨日总成交量
        self.pre_today_volume = 0
        self.pre_today_amount = 0
        self.pre_low = 0
        self.pre_high = 0
        self.pre_open = 0

    
    def add_tick(self, tick: TickData) -> None:
        """添加数据"""
        self.index += 1
        self.extra_tick(tick)
        self.timestamps.append(tick.timestamp)
        self.values.append(tick)
        self.ts_index[tick.timestamp] = self.index


    def extra_tick(self, tick: TickData) -> None:
        tick.index = self.index
        tick.change = round((tick.last_price - tick.pre_close) / tick.pre_close, 4) * 100
        self.bid_vol += tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5
        self.ask_vol += tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5
        if self.stop_status in (1, -1):
            if tick.ask_price_1 and tick.bid_price_1:
                self.stop_status = 0
        # 排除15：25之间的数据
        elif tick.volume:
            if not tick.ask_price_1:
                self.stop_status = 1
            elif not tick.bid_price_1:
                self.stop_status = -1
            elif round(tick.ask_price_1, 2) == round(tick.pre_close * self.limit_ratio, 2):
                self.stop_status = 2
        pre_tick: TickData = self.tick
        if not pre_tick:
            tick.net_amount = tick.amount
            tick.net_volume = tick.volume
            tick.max_bid_volume_1 = tick.bid_volume_1
            tick.max_ask_volume_1 = tick.ask_volume_1
            tick.max_bid_price_1 = tick.bid_price_1
            tick.max_ask_price_1 = tick.ask_price_1
            return
        tick.net_amount = tick.amount - pre_tick.amount
        tick.net_volume = tick.volume - pre_tick.volume
        tick.max_bid_volume_1 = max(tick.bid_volume_1, pre_tick.max_bid_volume_1)
        tick.max_ask_volume_1 = max(tick.ask_volume_1, pre_tick.max_ask_volume_1)
        tick.max_bid_price_1 = max(tick.bid_price_1, pre_tick.max_bid_price_1)
        tick.max_ask_price_1 = max(tick.ask_price_1, pre_tick.max_ask_price_1)
        if tick.last_price > pre_tick.last_price:
            tick.yang_volume += tick.net_volume
            tick.yang_amount += tick.net_amount
        elif tick.last_price < pre_tick.last_price:
            tick.yin_volume += tick.net_volume
            tick.yin_amount += tick.net_amount
    @property
    def tick(self):
        if not self.values:
            return None
        return self.values[-1]

    def get_exact_ts_index(self, timestamp: int):
        """精确查找"""
        return self.ts_index.get(timestamp, None)
    
    def get_ts_index(self, timestamps: int):
        # 找第一个 >= timestamps 的位置 索引
        index = self.get_exact_ts_index(timestamps)
        if index is not None:
            return index
        index = bisect.bisect_right(self.timestamps, timestamps)
        return index
    
    def get_index_tick(self, index: int):
        return self.values[index]

    def get_last_ticks_n(self, n: int):
        return self.values[-n:]

    def get_range_ticks_by_index(self, start_index: int, end_index: int):
        return self.values[start_index:end_index]

    def get_range_ticks(self, start_time: int, end_time: int):
        """查找某个时间的"""
        start_idx = self.get_ts_index(start_time)
        end_idx = self.get_ts_index(end_time)
        return self.values[start_idx:end_idx]

    def get_start_ticks(self, start_time: int, count: int):
        start_idx = self.get_ts_index(start_time)
        if count == -1:
            return self.values[start_idx:]
        else:
            return self.values[start_idx:start_idx+count]
    
    def get_end_ticks(self, end_time: int, count: int):
        end_idx = self.get_ts_index(end_time)
        return self.values[end_idx-count:end_idx]

    def get_tick_by_time(self, timestamp: int):
        index = self.get_ts_index(timestamp)
        return self.values[index]

    def get_ticks_by_price(self, price: float):
        """查找某个价格的tick"""
        ticks = [tick for tick in self.values if round(tick.last_price, 2) == round(price, 2)]
        return ticks
    
class CashManager:
    name = "cash_manager"
    def __init__(self) -> None:
        self.base_info = {}
        self.queue = Queue()
        self.vt_symbols: dict[str, CashTick] = {}
        self.thread = threading.Thread(target=self.on_ticks)
        self.queue_dct: dict[str, Queue] = {}
        self.code_list = []
    
    def on_init(self):
        # 读取统计信息文件
        stats_file = os.path.join(data_dir, SETTINGS["上一交易日"], "统计信息.xlsx")
        if not os.path.exists(stats_file):
            print(f"警告: 统计信息文件不存在: {stats_file}")
            return

        excle_reader = ExcelReader(stats_file)
        sheet_names = excle_reader.get_sheet_names()
        print(f"统计信息文件包含工作表: {sheet_names}")

        for name in sheet_names:
            df = excle_reader.read_sheet(name)
            self.base_info[name] = df

        # 检查是否存在股票池工作表
        if "股票池" not in self.base_info:
            print(f"错误: 统计信息文件中未找到'股票池'工作表")
            print(f"可用工作表: {list(self.base_info.keys())}")
            return

        # 检查是否存在昨日信息工作表
        if "昨日信息" not in self.base_info:
            print(f"错误: 统计信息文件中未找到'昨日信息'工作表")
            print(f"可用工作表: {list(self.base_info.keys())}")
            return

        # 读取股票基本信息
        base_info_file = os.path.join(data_dir, "股票基本信息.xlsx")
        if not os.path.exists(base_info_file):
            print(f"警告: 股票基本信息文件不存在: {base_info_file}")
            return

        excle_reader = ExcelReader(base_info_file)
        base_info = excle_reader.read_sheet("股票")

        # 获取股票代码列表
        stock_pool_df = self.base_info["股票池"]
        if "股票代码" not in stock_pool_df.columns:
            print(f"错误: 股票池工作表中未找到'股票代码'列")
            print(f"可用列: {list(stock_pool_df.columns)}")
            return

        code_info = stock_pool_df["股票代码"].tolist()
        pre_day_df = self.base_info["昨日信息"]
        print(f"开始处理 {len(code_info)} 个股票代码...")

        for vt_symbol in code_info:
            try:
                self.code_list.append(vt_symbol)
                cash_tick = CashTick()
                # 查找股票基本信息
                vt_info = base_info[base_info["股票代码"] == vt_symbol]
                # 查找昨日信息
                pre_vt_info = pre_day_df[pre_day_df["股票代码"] == vt_symbol]
                # 提取数据
                vt_pre_close = pre_vt_info["收盘价"].values[0]
                cash_tick.limit_ratio = vt_info["最大涨跌幅"].values[0]
                cash_tick.float_volume = float(vt_info["流通股本"].values[0])
                cash_tick.total_volume = float(vt_info["总股本"].values[0])
                cash_tick.limit_up_price = round(float(vt_pre_close * (1 + cash_tick.limit_ratio)), 2)
                cash_tick.limit_down_price = round(float(vt_pre_close * (1 - cash_tick.limit_ratio)), 2)
                cash_tick.pre_today_volume = pre_vt_info["成交量"].values[0]
                cash_tick.pre_today_amount = pre_vt_info["成交额"].values[0]
                cash_tick.pre_low = pre_vt_info["最低价"].values[0]
                cash_tick.pre_high = pre_vt_info["最高价"].values[0]
                cash_tick.pre_open = pre_vt_info["开盘价"].values[0]
                self.vt_symbols[vt_symbol] = cash_tick

            except Exception as e:
                print(f"处理股票 {vt_symbol} 时出错: {str(e)}")
                continue

        print(f"成功处理了 {len(self.vt_symbols)} 个股票")
    
    def add_queue(self, queue: Queue, key: str):
        if key in self.queue_dct:
            return
        self.queue_dct[key] = queue

    def remove_queue(self, key: str):
        if key in self.queue_dct:
            del self.queue_dct[key]
    
    def on_ticks(self) -> None:
        while self.active:
            try:
                ticks = self.queue.get(timeout=0.5)
                if not ticks:
                    continue
                for tick in ticks:
                    cash_tick = self.vt_symbols.get(tick.vt_symbol, None)
                    if not cash_tick or tick.open_interest == 12:
                        continue
                    cash_tick.add_tick(tick)
                    for queue in self.queue_dct.values():
                        queue.put(tick)
                
            except Empty:
                continue

    def start(self):
        self.active = True
        self.thread.start()

    def close(self):
        self.active = False
        self.thread.join()

    